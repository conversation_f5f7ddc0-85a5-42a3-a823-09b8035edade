/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    overflow-x: hidden;
}

/* 顶部导航栏 */
.top-header {
    background: white;
    border-bottom: 1px solid #e5e5e5;
    padding: 0 20px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: bold;
}

.nox-text {
    color: #FF6B35;
}

.star-text {
    color: #333;
}

.page-title {
    font-size: 16px;
    font-weight: normal;
    color: #666;
    margin: 0;
}

.header-center {
    flex: 1;
    max-width: 400px;
    margin: 0 40px;
}

.search-box {
    position: relative;
    width: 100%;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.search-box input {
    width: 100%;
    padding: 8px 12px 8px 35px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-btn {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    color: #666;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.header-btn:hover {
    background-color: #f5f5f5;
}

.price-btn {
    background-color: #FF6B35;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.language-selector {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    color: #666;
    font-size: 14px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    background-color: #FF6B35;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

/* 主容器 */
.main-container {
    display: flex;
    margin-top: 60px;
    min-height: calc(100vh - 60px);
}

/* 左侧导航栏 */
.sidebar {
    width: 200px;
    background-color: #2c3e50;
    color: white;
    padding: 0;
    position: fixed;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    overflow-y: auto;
}

.sidebar-nav {
    padding: 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    font-size: 14px;
}

.nav-item:hover {
    background-color: rgba(255,255,255,0.1);
}

.nav-item.active {
    background-color: #FF6B35;
}

.nav-item i {
    width: 16px;
    text-align: center;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: 200px;
    padding: 20px;
    background-color: #f5f5f5;
}

/* 网红信息卡片 */
.influencer-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.update-time {
    font-size: 12px;
    color: #999;
    margin-bottom: 15px;
}

.update-status {
    color: #52c41a;
    margin-left: 10px;
}

.influencer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.avatar-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
}

.basic-info .name {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #333;
}

.username {
    color: #999;
    font-weight: normal;
    font-size: 14px;
}

.location {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
}

.flag {
    width: 16px;
    height: 12px;
}

.verified {
    color: #52c41a;
}

.subscriber-count {
    color: #FF6B35;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

.action-buttons button {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.btn-ai-similar {
    background-color: #FF6B35;
    color: white;
    border-color: #FF6B35;
}

.btn-youtube {
    background-color: #ff0000;
    color: white;
    border-color: #ff0000;
}

.action-buttons button:hover {
    opacity: 0.8;
}

/* 关键指标 */
.key-metrics {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.metric-item {
    flex: 1;
    background: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.metric-label {
    font-size: 12px;
    color: #999;
    margin-bottom: 5px;
}

.metric-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

/* 数据分类标签 */
.data-tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 20px;
    background: white;
    border-radius: 8px;
    padding: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tab-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 6px;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tab-btn.active {
    background-color: #FF6B35;
    color: white;
}

.tab-btn:hover:not(.active) {
    background-color: #f5f5f5;
}

/* 基本数据区域 */
.basic-data {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.section-title i {
    color: #FF6B35;
}

.time-filter {
    margin-left: auto;
    font-size: 12px;
    color: #999;
    font-weight: normal;
}

/* 数据网格 */
.data-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
}

.data-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.data-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.data-card h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
    font-weight: normal;
}

.data-value {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.data-change {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.data-change.positive {
    color: #52c41a;
}

.data-change.negative {
    color: #ff4d4f;
}

.data-subtitle {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

/* 增长数据区域 */
.growth-data {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.growth-cards {
    display: flex;
    gap: 20px;
}

.growth-card {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.growth-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.growth-icon.blue {
    background-color: #1890ff;
}

.growth-icon.green {
    background-color: #52c41a;
}

.growth-icon.orange {
    background-color: #fa8c16;
}

.growth-info .growth-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.growth-info .growth-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* 分页组件 */
.pagination-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination-tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 20px;
    background: #f5f5f5;
    border-radius: 6px;
    padding: 4px;
}

.pagination-tab {
    flex: 1;
    padding: 8px 16px;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
}

.pagination-tab.active {
    background-color: white;
    color: #333;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.pagination-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.pagination-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
    background-color: #f5f5f5;
    border-color: #FF6B35;
}

.pagination-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.pagination-numbers {
    display: flex;
    gap: 5px;
}

.page-number {
    width: 32px;
    height: 32px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
}

.page-number:hover {
    background-color: #f5f5f5;
    border-color: #FF6B35;
}

.page-number.active {
    background-color: #FF6B35;
    border-color: #FF6B35;
    color: white;
}

/* 动画 */
@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-transition {
    transition: opacity 0.3s ease;
}

.fade-out {
    opacity: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .data-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .growth-cards {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .sidebar {
        position: relative;
        width: 100%;
        height: auto;
        top: 0;
    }

    .main-content {
        margin-left: 0;
    }

    .header-center {
        display: none;
    }

    .header-right {
        gap: 10px;
    }

    .influencer-info {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .key-metrics {
        flex-wrap: wrap;
    }

    .data-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .pagination-controls {
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .data-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-wrap: wrap;
    }

    .key-metrics {
        flex-direction: column;
    }
}
