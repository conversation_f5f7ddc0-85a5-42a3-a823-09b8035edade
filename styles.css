/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    overflow-x: hidden;
}

/* 顶部导航栏 */
.top-header {
    background: white;
    border-bottom: 1px solid #e5e5e5;
    padding: 0 20px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: bold;
}

.nox-text {
    color: #FF6B35;
}

.star-text {
    color: #333;
}

.page-title {
    font-size: 16px;
    font-weight: normal;
    color: #666;
    margin: 0;
}

.header-center {
    flex: 1;
    max-width: 400px;
    margin: 0 40px;
}

.search-box {
    position: relative;
    width: 100%;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.search-box input {
    width: 100%;
    padding: 8px 12px 8px 35px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-btn {
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    color: #666;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.header-btn:hover {
    background-color: #f5f5f5;
}

.price-btn {
    background-color: #FF6B35;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.language-selector {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    color: #666;
    font-size: 14px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    background-color: #FF6B35;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

/* 主容器 */
.main-container {
    display: flex;
    margin-top: 60px;
    min-height: calc(100vh - 60px);
}

/* 左侧导航栏 */
.sidebar {
    width: 200px;
    background-color: #2c3e50;
    color: white;
    padding: 0;
    position: fixed;
    left: 0;
    top: 60px;
    height: calc(100vh - 60px);
    overflow-y: auto;
}

.sidebar-nav {
    padding: 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    font-size: 14px;
}

.nav-item:hover {
    background-color: rgba(255,255,255,0.1);
}

.nav-item.active {
    background-color: #FF6B35;
}

.nav-item i {
    width: 16px;
    text-align: center;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: 200px;
    padding: 20px;
    background-color: #f5f5f5;
}

/* 统一的Bento Grid卡片系统 */
.bento-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.bento-card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

/* 统一的卡片头部 */
.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.card-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-header i {
    color: #FF6B35;
}

/* 网红信息卡片 */
.influencer-info-card {
    margin-bottom: 20px;
}

/* 更新时间 */
.update-time {
    font-size: 12px;
    color: #999;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.update-status {
    background-color: #f6ffed;
    color: #52c41a;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
}

/* 网红内容区域 */
.influencer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.influencer-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #f0f0f0;
}

.basic-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* 姓名行 */
.name-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 4px;
}

.name {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    margin: 0;
}

.username {
    color: #666;
    font-size: 14px;
}

.category {
    background-color: #f0f0f0;
    color: #666;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* 位置行 */
.location-row {
    display: flex;
    align-items: center;
    gap: 8px;
}

.flag {
    width: 16px;
    height: 12px;
    border-radius: 2px;
    object-fit: cover;
}

.country {
    font-size: 14px;
    color: #666;
}

.verified {
    color: #52c41a;
    font-size: 12px;
    font-weight: 500;
}

.subscriber-count {
    background-color: #fff7e6;
    color: #fa8c16;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 500;
}

.flag {
    width: 16px;
    height: 12px;
}

.verified {
    color: #52c41a;
}

/* 右侧操作按钮 */
.influencer-right {
    display: flex;
    align-items: center;
}

.action-buttons {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* AI分析按钮 */
.btn-ai {
    background-color: #FF6B35;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
}

.btn-ai:hover {
    background-color: #e55a2b;
    transform: translateY(-1px);
}

/* 图标按钮 */
.btn-icon {
    width: 36px;
    height: 36px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #666;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    border-color: #FF6B35;
    color: #FF6B35;
    transform: translateY(-1px);
}

/* YouTube按钮 */
.btn-youtube {
    background-color: #ff0000;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
}

.btn-youtube:hover {
    background-color: #cc0000;
    transform: translateY(-1px);
}

/* 关键指标卡片组 */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
    margin-bottom: 20px;
}

.metric-card {
    text-align: center;
    padding: 20px 16px;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.metric-header {
    font-size: 12px;
    color: #999;
    margin-bottom: 8px;
    line-height: 1.2;
}

.metric-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    line-height: 1.2;
}



/* 数据分类标签卡片 */
.data-tabs-card {
    margin-bottom: 20px;
}

/* 数据标签页 */
.data-tabs-section {
    margin-bottom: 20px;
}

.tab-buttons {
    display: flex;
    gap: 0;
    border-bottom: 1px solid #f0f0f0;
}

.tab-button {
    padding: 12px 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 6px;
    border-bottom: 2px solid transparent;
    position: relative;
}

.tab-button:hover {
    color: #FF6B35;
}

.tab-button.active {
    color: #FF6B35;
    border-bottom-color: #FF6B35;
    font-weight: 500;
}

.tab-button i {
    font-size: 16px;
}

/* 基本数据卡片 */
.basic-data-card {
    margin-bottom: 20px;
}

.time-filter {
    background-color: #f0f0f0;
    color: #666;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

/* 合作价格和CPM区域 */
.cooperation-pricing-section {
    margin-bottom: 30px;
}

.section-header {
    margin-bottom: 20px;
}

.section-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-header i {
    color: #FF6B35;
}

.pricing-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 20px;
}

/* CPM卡片样式 */
.cpm-card {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 200px;
}

.cpm-value {
    display: flex;
    align-items: baseline;
    gap: 4px;
    margin: 15px 0;
}

.cpm-value .currency {
    font-size: 24px;
    font-weight: 600;
    color: #FF6B35;
}

.cpm-value .amount {
    font-size: 36px;
    font-weight: 700;
    color: #FF6B35;
}

.cpm-description {
    font-size: 12px;
    color: #666;
    margin: 8px 0;
    line-height: 1.4;
}

.cpm-note {
    font-size: 11px;
    color: #999;
    line-height: 1.4;
}

/* 植入视频价格卡片样式 */
.video-pricing-card {
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 200px;
}

.pricing-value {
    display: flex;
    align-items: baseline;
    gap: 4px;
    margin: 15px 0;
}

.pricing-value .currency {
    font-size: 20px;
    font-weight: 600;
    color: #FF6B35;
}

.pricing-value .amount-range {
    font-size: 24px;
    font-weight: 700;
    color: #FF6B35;
}

.pricing-description {
    font-size: 12px;
    color: #666;
    margin: 8px 0;
    line-height: 1.4;
}

.pricing-note {
    font-size: 11px;
    color: #999;
    line-height: 1.4;
}

/* 频道粉丝数排名区域 */
.ranking-section {
    margin-bottom: 30px;
}

.ranking-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* 排名卡片样式 */
.ranking-card {
    display: flex;
    align-items: center;
    gap: 15px;
    min-height: 120px;
    padding: 20px;
}

.ranking-icon {
    width: 50px;
    height: 50px;
    background-color: #fff7e6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.ranking-icon i {
    font-size: 20px;
    color: #FF6B35;
}

.ranking-content {
    flex: 1;
}

.ranking-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.ranking-value {
    display: flex;
    align-items: baseline;
    gap: 8px;
}

.rank-number {
    font-size: 32px;
    font-weight: 700;
    color: #FF6B35;
}

.rank-suffix {
    font-size: 14px;
    color: #999;
}

/* 受众数据页面样式 */
.audience-data-section {
    display: none;
}

.audience-data-section.active {
    display: block;
}

/* 受众概览网格 */
.audience-overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.audience-stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.2s, box-shadow 0.2s;
}

.audience-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #FF6B35, #FF8A65);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-icon i {
    color: white;
    font-size: 20px;
}

.stat-content {
    flex: 1;
}

.stat-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.stat-unit,
.stat-percentage {
    font-size: 14px;
    color: #999;
}

.stat-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.stat-status.excellent {
    background-color: #e8f5e8;
    color: #4caf50;
}

/* 受众分析网格 */
.audience-analysis-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.audience-chart-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.audience-chart-card .card-header {
    margin-bottom: 20px;
}

.audience-chart-card h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

/* 地区分布样式 */
.region-distribution {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
    align-items: start;
}

.region-bar-chart {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.region-item {
    display: grid;
    grid-template-columns: 80px 1fr 60px;
    align-items: center;
    gap: 12px;
}

.region-label {
    font-size: 12px;
    color: #666;
    text-align: right;
}

.region-bar {
    height: 20px;
    background-color: #f5f5f5;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.region-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.8s ease;
}

.region-fill[data-color="#4285F4"] { background-color: #4285F4; }
.region-fill[data-color="#34A853"] { background-color: #34A853; }
.region-fill[data-color="#FBBC04"] { background-color: #FBBC04; }
.region-fill[data-color="#EA4335"] { background-color: #EA4335; }
.region-fill[data-color="#9C27B0"] { background-color: #9C27B0; }
.region-fill[data-color="#FF9800"] { background-color: #FF9800; }
.region-fill[data-color="#607D8B"] { background-color: #607D8B; }

.region-percentage {
    font-size: 12px;
    font-weight: 600;
    color: #333;
    text-align: center;
}

.region-map-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 语言分布样式 */
.language-distribution {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.language-item {
    display: grid;
    grid-template-columns: 80px 1fr 60px;
    align-items: center;
    gap: 12px;
}

.language-label {
    font-size: 12px;
    color: #666;
    text-align: right;
}

.language-bar {
    height: 24px;
    background-color: #f5f5f5;
    border-radius: 12px;
    overflow: hidden;
}

.language-fill {
    height: 100%;
    background: linear-gradient(90deg, #FF6B35, #FF8A65);
    border-radius: 12px;
    transition: width 0.8s ease;
}

.language-percentage {
    font-size: 12px;
    font-weight: 600;
    color: #333;
    text-align: center;
}

/* 人口统计网格 */
.demographics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.age-chart-container,
.gender-chart-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
}

.chart-note {
    font-size: 11px;
    color: #999;
    line-height: 1.4;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .audience-analysis-grid,
    .demographics-grid {
        grid-template-columns: 1fr;
    }

    .region-distribution {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .region-map-container {
        order: -1;
    }
}

@media (max-width: 768px) {
    .audience-overview-grid {
        grid-template-columns: 1fr;
    }

    .audience-stat-card {
        padding: 15px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
    }

    .stat-icon i {
        font-size: 16px;
    }

    .region-item,
    .language-item {
        grid-template-columns: 60px 1fr 50px;
        gap: 8px;
    }

    .region-label,
    .language-label {
        font-size: 11px;
    }

    .region-bar,
    .language-bar {
        height: 18px;
    }
}

/* 基本数据区域 */
.basic-data {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.section-title i {
    color: #FF6B35;
}

.time-filter {
    margin-left: auto;
    font-size: 12px;
    color: #999;
    font-weight: normal;
}

/* 数据网格 */
.data-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
}

.data-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.data-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.data-card h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
    font-weight: normal;
}

.data-value {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.data-change {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.data-change.positive {
    color: #52c41a;
}

.data-change.negative {
    color: #ff4d4f;
}

.data-subtitle {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

/* 增长数据区域 */
.growth-data {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.growth-cards {
    display: flex;
    gap: 20px;
}

.growth-card {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.growth-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.growth-icon.blue {
    background-color: #1890ff;
}

.growth-icon.green {
    background-color: #52c41a;
}

.growth-icon.orange {
    background-color: #fa8c16;
}

.growth-info .growth-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.growth-info .growth-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* 增长数据标签和图表控制 */
.growth-tabs-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 30px 0 20px 0;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e5e5;
}

.growth-tabs {
    display: flex;
    gap: 0;
}

.growth-tab {
    padding: 10px 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    position: relative;
    transition: all 0.3s ease;
}

.growth-tab.active {
    color: #FF6B35;
}

.growth-tab.active::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #FF6B35;
}

.growth-tab:hover:not(.active) {
    color: #333;
}

.chart-controls {
    display: flex;
    gap: 5px;
}

.chart-control-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    transition: all 0.2s;
}

.chart-control-btn.active {
    background-color: #FF6B35;
    border-color: #FF6B35;
    color: white;
}

.chart-control-btn:hover:not(.active) {
    background-color: #f5f5f5;
    border-color: #FF6B35;
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.chart-container canvas {
    max-height: 100%;
}

/* 图表说明 */
.chart-description {
    margin-top: 15px;
}

.chart-note {
    font-size: 12px;
    color: #999;
    margin: 5px 0;
}

/* 频道质量区域 */
.channel-quality {
    margin-bottom: 20px;
}

/* Bento Grid 卡片布局 */
.quality-bento-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 20px;
    margin-top: 20px;
}

/* 基础卡片样式 */
.quality-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.quality-card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

/* 卡片布局定义 */
.nox-score-card {
    grid-column: 1;
    grid-row: 1;
}

.radar-chart-card {
    grid-column: 2;
    grid-row: 1;
}

.detailed-scores-card {
    grid-column: 3;
    grid-row: 1;
}

.cooperation-score-card {
    grid-column: 1;
    grid-row: 2;
}

.cooperation-tags-card {
    grid-column: 2 / -1;
    grid-row: 2;
}

/* 卡片头部样式 */
.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.card-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* NoxScore评分卡片 */
.nox-score-card {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 280px;
}

.score-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.score-badge.excellent {
    background-color: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.score-display {
    display: flex;
    align-items: baseline;
    margin-bottom: 10px;
}

.score-number {
    font-size: 36px;
    font-weight: 700;
    color: #333;
}

.score-suffix {
    font-size: 16px;
    color: #666;
    margin-left: 4px;
}

.star-rating {
    display: flex;
    gap: 2px;
    margin-bottom: 15px;
}

.star-rating i {
    color: #fadb14;
    font-size: 16px;
}

.card-description {
    font-size: 12px;
    color: #666;
    line-height: 1.6;
    margin: 0;
    margin-top: auto;
}

/* 雷达图卡片 */
.radar-chart-card {
    display: flex;
    flex-direction: column;
    text-align: center;
    min-height: 280px;
}

.radar-container {
    position: relative;
    height: 220px;
    width: 220px;
    margin: 0 auto;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.radar-container canvas {
    max-width: 100%;
    max-height: 100%;
}

/* 详细指标评分卡片 */
.detailed-scores-card {
    min-height: 280px;
}

.detailed-scores {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.score-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.score-item:last-child {
    border-bottom: none;
}

.score-label {
    font-size: 14px;
    color: #333;
    flex: 1;
}

.score-stars {
    display: flex;
    gap: 2px;
    margin: 0 15px;
}

.score-stars i {
    font-size: 14px;
    color: #fadb14;
}

.score-stars .far {
    color: #d9d9d9;
}

.score-level {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
}

.score-level.excellent {
    background-color: #f6ffed;
    color: #52c41a;
}

.score-level.good {
    background-color: #e6f7ff;
    color: #1890ff;
}

.score-level.average {
    background-color: #fff7e6;
    color: #fa8c16;
}

/* 合作指向卡片 */
.cooperation-score-card {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.cooperation-rating {
    display: flex;
    align-items: baseline;
    gap: 4px;
    margin: 15px 0;
}

.cooperation-number {
    font-size: 36px;
    font-weight: 700;
    color: #FF6B35;
}

.cooperation-divider {
    font-size: 24px;
    color: #999;
}

.cooperation-total {
    font-size: 24px;
    color: #999;
}

.cooperation-description,
.cooperation-note {
    font-size: 12px;
    color: #666;
    margin: 4px 0;
    line-height: 1.4;
}

/* 合作行为标签卡片 */
.cooperation-tags-card {
    min-height: 200px;
    display: flex;
    flex-direction: column;
}

.tags-indicator {
    background-color: #fff7e6;
    color: #fa8c16;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}



.tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 15px;
}

.tag {
    background-color: #f0f0f0;
    color: #666;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
}

.tag:hover {
    background-color: #ffd591;
    color: #d46b08;
}

/* 分页组件 */
.pagination-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pagination-tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 20px;
    background: #f5f5f5;
    border-radius: 6px;
    padding: 4px;
}

.pagination-tab {
    flex: 1;
    padding: 8px 16px;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
}

.pagination-tab.active {
    background-color: white;
    color: #333;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.pagination-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.pagination-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
    background-color: #f5f5f5;
    border-color: #FF6B35;
}

.pagination-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.pagination-numbers {
    display: flex;
    gap: 5px;
}

.page-number {
    width: 32px;
    height: 32px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #666;
    transition: all 0.2s;
}

.page-number:hover {
    background-color: #f5f5f5;
    border-color: #FF6B35;
}

.page-number.active {
    background-color: #FF6B35;
    border-color: #FF6B35;
    color: white;
}

/* 动画 */
@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-transition {
    transition: opacity 0.3s ease;
}

.fade-out {
    opacity: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .data-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .growth-cards {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .sidebar {
        position: relative;
        width: 100%;
        height: auto;
        top: 0;
    }

    .main-content {
        margin-left: 0;
    }

    .header-center {
        display: none;
    }

    .header-right {
        gap: 10px;
    }

    /* 网红信息卡片响应式 */
    .influencer-content {
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }

    .influencer-left {
        width: 100%;
    }

    .influencer-right {
        width: 100%;
    }

    .action-buttons {
        flex-wrap: wrap;
        gap: 8px;
    }

    /* 关键指标卡片响应式 */
    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    /* 数据标签页响应式 */
    .tab-buttons {
        flex-wrap: wrap;
        gap: 8px;
        border-bottom: none;
    }

    .tab-button {
        border: 1px solid #ddd;
        border-radius: 6px;
        border-bottom: 2px solid transparent;
    }

    .tab-button.active {
        border-color: #FF6B35;
        background-color: #FF6B35;
        color: white;
    }

    .data-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .pagination-controls {
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .data-grid {
        grid-template-columns: 1fr;
    }

    /* 小屏幕响应式 */
    .influencer-left {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .name-row {
        flex-direction: column;
        gap: 4px;
        text-align: center;
    }

    .location-row {
        justify-content: center;
        flex-wrap: wrap;
    }

    .action-buttons {
        justify-content: center;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .growth-tabs-container {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }

    .growth-tabs {
        width: 100%;
        justify-content: space-between;
    }

    .growth-tab {
        flex: 1;
        text-align: center;
        padding: 8px 10px;
        font-size: 12px;
    }

    .chart-container {
        height: 300px;
        padding: 15px;
    }

    .quality-bento-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .nox-score-card,
    .radar-chart-card,
    .detailed-scores-card,
    .cooperation-score-card,
    .cooperation-tags-card {
        grid-column: 1;
        grid-row: auto;
        min-height: auto;
    }

    .cooperation-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .radar-container {
        height: 180px;
        width: 180px;
    }

    /* 合作价格和排名区域响应式 */
    .pricing-grid,
    .ranking-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .ranking-card {
        min-height: 100px;
        padding: 15px;
    }

    .ranking-icon {
        width: 40px;
        height: 40px;
    }

    .ranking-icon i {
        font-size: 16px;
    }

    .rank-number {
        font-size: 24px;
    }

    .cpm-value .amount,
    .pricing-value .amount-range {
        font-size: 20px;
    }
}
