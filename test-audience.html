<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>受众数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .audience-analysis-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .audience-chart-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .card-header h4 {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0 0 20px 0;
        }
        
        .horizontal-bar-section {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .horizontal-bar {
            display: flex;
            width: 100%;
            height: 24px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .bar-segment {
            height: 100%;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .bar-segment:hover {
            opacity: 0.8;
            transform: scaleY(1.1);
        }
        
        .region-detail-list,
        .language-detail-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 10px;
        }
        
        .detail-item {
            display: grid;
            grid-template-columns: 80px 1fr 60px;
            align-items: center;
            gap: 12px;
        }
        
        .detail-label {
            font-size: 12px;
            color: #666;
            text-align: right;
            font-weight: 500;
        }
        
        .detail-bar {
            height: 20px;
            background-color: #f5f5f5;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .detail-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.8s ease;
        }
        
        .detail-percentage {
            font-size: 12px;
            font-weight: 600;
            color: #333;
            text-align: center;
        }
        
        .chart-note {
            font-size: 11px;
            color: #999;
            line-height: 1.4;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>受众数据测试页面</h1>
        
        <div class="audience-analysis-grid">
            <!-- 受众区域分布 -->
            <div class="audience-chart-card region-card">
                <div class="card-header">
                    <h4>受众区域</h4>
                </div>
                
                <!-- 横向条形图 -->
                <div class="horizontal-bar-section">
                    <div class="horizontal-bar-container">
                        <div class="horizontal-bar">
                            <div class="bar-segment" style="background-color: #4285F4; width: 68.7%;"></div>
                            <div class="bar-segment" style="background-color: #34A853; width: 9.8%;"></div>
                            <div class="bar-segment" style="background-color: #FBBC04; width: 4.7%;"></div>
                            <div class="bar-segment" style="background-color: #EA4335; width: 4.3%;"></div>
                            <div class="bar-segment" style="background-color: #9C27B0; width: 3.5%;"></div>
                            <div class="bar-segment" style="background-color: #FF9800; width: 3.1%;"></div>
                            <div class="bar-segment" style="background-color: #607D8B; width: 5.9%;"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 详细地区列表 -->
                <div class="region-detail-list">
                    <div class="detail-item">
                        <span class="detail-label">美国</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 68.7%; background-color: #4285F4;"></div>
                        </div>
                        <span class="detail-percentage">68.7%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">加拿大</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 9.8%; background-color: #34A853;"></div>
                        </div>
                        <span class="detail-percentage">9.8%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">英国</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 4.7%; background-color: #FBBC04;"></div>
                        </div>
                        <span class="detail-percentage">4.7%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">澳大利亚</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 4.3%; background-color: #EA4335;"></div>
                        </div>
                        <span class="detail-percentage">4.3%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">印度</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 3.5%; background-color: #9C27B0;"></div>
                        </div>
                        <span class="detail-percentage">3.5%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">德国</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 3.1%; background-color: #FF9800;"></div>
                        </div>
                        <span class="detail-percentage">3.1%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">其他</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 5.9%; background-color: #607D8B;"></div>
                        </div>
                        <span class="detail-percentage">5.9%</span>
                    </div>
                </div>
                
                <div class="chart-note">
                    受众地理数据基于过去30天的观看数据统计，通过IP地址分析得出地理位置分布
                </div>
            </div>

            <!-- 受众语言分布 -->
            <div class="audience-chart-card language-card">
                <div class="card-header">
                    <h4>受众语言</h4>
                </div>
                
                <!-- 横向条形图 -->
                <div class="horizontal-bar-section">
                    <div class="horizontal-bar-container">
                        <div class="horizontal-bar">
                            <div class="bar-segment" style="background-color: #4285F4; width: 84.5%;"></div>
                            <div class="bar-segment" style="background-color: #FF9800; width: 7.3%;"></div>
                            <div class="bar-segment" style="background-color: #34A853; width: 6.4%;"></div>
                            <div class="bar-segment" style="background-color: #607D8B; width: 1.8%;"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 详细语言列表 -->
                <div class="language-detail-list">
                    <div class="detail-item">
                        <span class="detail-label">英语</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 84.5%; background-color: #4285F4;"></div>
                        </div>
                        <span class="detail-percentage">84.5%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">西班牙语</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 7.3%; background-color: #FF9800;"></div>
                        </div>
                        <span class="detail-percentage">7.3%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">葡萄牙语</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 6.4%; background-color: #34A853;"></div>
                        </div>
                        <span class="detail-percentage">6.4%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">其他语言</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 1.8%; background-color: #607D8B;"></div>
                        </div>
                        <span class="detail-percentage">1.8%</span>
                    </div>
                </div>
                
                <div class="chart-note">
                    受众语言数据基于用户设备语言设置和观看行为分析，通过机器学习算法识别用户语言偏好
                </div>
            </div>
        </div>
    </div>
</body>
</html>
