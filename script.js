// NOX聚星数据总览系统
class NoxInfluencerDashboard {
    constructor() {
        this.currentPage = 1;
        this.totalPages = 5;
        this.currentTab = 'overview';
        this.currentDataType = 'fans';
        this.currentChartType = 'line';
        this.chart = null;

        this.initializeElements();
        this.generateMockData();
        this.bindEvents();
        this.render();
        this.initializeChart();
    }
    
    // 初始化DOM元素
    initializeElements() {
        this.elements = {
            dataGrid: document.getElementById('dataGrid'),
            prevBtn: document.getElementById('prevBtn'),
            nextBtn: document.getElementById('nextBtn'),
            paginationNumbers: document.getElementById('paginationNumbers'),
            tabBtns: document.querySelectorAll('.tab-btn'),
            paginationTabs: document.querySelectorAll('.pagination-tab'),
            growthTabs: document.querySelectorAll('.growth-tab'),
            chartControlBtns: document.querySelectorAll('.chart-control-btn'),
            chartCanvas: document.getElementById('growthChart'),
            fansCount: document.getElementById('fansCount'),
            viewsCount: document.getElementById('viewsCount'),
            commentsCount: document.getElementById('commentsCount')
        };
    }
    
    // 生成模拟数据
    generateMockData() {
        this.basicData = [
            {
                title: '平均观看量',
                value: '997.04万',
                change: '*****%',
                changeType: 'positive',
                subtitle: '6个月 1.06.04万 Shorts 810.74万',
                description: '各项指标表现良好，观看量稳定增长'
            },
            {
                title: '平均互动量',
                value: '44.91万',
                change: '+13.42%',
                changeType: 'positive',
                subtitle: '长视频 35.34万 Shorts 32.31万',
                description: '互动率持续提升，用户参与度高'
            },
            {
                title: '内容数量',
                value: '30',
                change: '0%',
                changeType: 'neutral',
                subtitle: '长视频 16 Shorts 14',
                description: '内容发布频率稳定'
            },
            {
                title: '观看量/粉丝数',
                value: '14.51%',
                change: '0%',
                changeType: 'neutral',
                subtitle: '长视频 18.48% Shorts 11.8%',
                description: '粉丝转化率表现良好'
            },
            {
                title: '预计曝光量',
                value: '982.61万',
                change: '',
                changeType: 'neutral',
                subtitle: '长视频 1099.03万 Shorts 774.76万',
                description: '预计曝光量持续增长'
            }
        ];

        // 生成增长数据
        this.generateGrowthData();
    }

    // 生成增长数据
    generateGrowthData() {
        const dates = [];
        const today = new Date();

        // 生成过去20天的日期
        for (let i = 19; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }));
        }

        this.growthData = {
            dates: dates,
            fans: {
                label: '粉丝数增长',
                data: this.generateRandomGrowthData(20, -2, 4),
                borderColor: '#1890ff',
                backgroundColor: 'rgba(24, 144, 255, 0.1)',
                barColor: '#1890ff'
            },
            views: {
                label: '播放量增长',
                data: this.generateRandomGrowthData(20, -3, 6),
                borderColor: '#52c41a',
                backgroundColor: 'rgba(82, 196, 26, 0.1)',
                barColor: '#52c41a'
            },
            posts: {
                label: '发布量增长',
                data: this.generateRandomGrowthData(20, -1, 3),
                borderColor: '#fa8c16',
                backgroundColor: 'rgba(250, 140, 22, 0.1)',
                barColor: '#fa8c16'
            }
        };
    }

    // 生成随机增长数据
    generateRandomGrowthData(count, min, max) {
        const data = [];
        for (let i = 0; i < count; i++) {
            const value = Math.random() * (max - min) + min;
            data.push(Math.round(value * 100) / 100);
        }
        return data;
    }
    
    // 绑定事件
    bindEvents() {
        // 分页按钮事件
        this.elements.prevBtn.addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.goToPage(this.currentPage - 1);
            }
        });
        
        this.elements.nextBtn.addEventListener('click', () => {
            if (this.currentPage < this.totalPages) {
                this.goToPage(this.currentPage + 1);
            }
        });
        
        // 数据标签切换事件
        this.elements.tabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.target.dataset.tab;
                if (tab) {
                    this.switchTab(tab);
                }
            });
        });
        
        // 分页标签切换事件
        this.elements.paginationTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchDataType(e.target.textContent);
            });
        });

        // 增长数据标签切换事件
        this.elements.growthTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const dataType = e.target.dataset.type;
                if (dataType) {
                    this.switchGrowthDataType(dataType);
                }
            });
        });

        // 图表控制按钮事件
        this.elements.chartControlBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const chartType = e.target.closest('.chart-control-btn').dataset.chart;
                if (chartType) {
                    this.switchChartType(chartType);
                }
            });
        });
    }
    
    // 切换数据标签
    switchTab(tab) {
        this.currentTab = tab;
        
        // 更新标签状态
        this.elements.tabBtns.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.tab === tab) {
                btn.classList.add('active');
            }
        });
        
        // 模拟不同标签的数据加载
        this.render();
    }
    
    // 切换数据类型
    switchDataType(type) {
        this.currentDataType = type;
        
        // 更新分页标签状态
        this.elements.paginationTabs.forEach(tab => {
            tab.classList.remove('active');
            if (tab.textContent.includes(type.replace('增长', ''))) {
                tab.classList.add('active');
            }
        });
        
        // 重置到第一页
        this.currentPage = 1;
        this.render();
    }
    
    // 切换增长数据类型
    switchGrowthDataType(dataType) {
        this.currentDataType = dataType;

        // 更新标签状态
        this.elements.growthTabs.forEach(tab => {
            tab.classList.remove('active');
            if (tab.dataset.type === dataType) {
                tab.classList.add('active');
            }
        });

        // 更新图表
        this.updateChart();
    }

    // 切换图表类型
    switchChartType(chartType) {
        this.currentChartType = chartType;

        // 更新按钮状态
        this.elements.chartControlBtns.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.chart === chartType) {
                btn.classList.add('active');
            }
        });

        // 更新图表
        this.updateChart();
    }

    // 跳转到指定页面
    goToPage(page) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) {
            return;
        }

        this.currentPage = page;
        this.render();
    }
    
    // 渲染基本数据卡片
    renderDataCards() {
        if (this.currentTab !== 'overview') {
            this.elements.dataGrid.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #999;">
                    <i class="fas fa-chart-line" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                    <h3>敬请期待</h3>
                    <p>${this.getTabName(this.currentTab)}数据功能正在开发中</p>
                </div>
            `;
            return;
        }
        
        this.elements.dataGrid.innerHTML = this.basicData.map(item => `
            <div class="data-card">
                <h4>${item.title}</h4>
                <div class="data-value">${item.value}</div>
                ${item.change ? `
                    <div class="data-change ${item.changeType}">
                        ${item.changeType === 'positive' ? '<i class="fas fa-arrow-up"></i>' : 
                          item.changeType === 'negative' ? '<i class="fas fa-arrow-down"></i>' : ''}
                        ${item.change}
                    </div>
                ` : ''}
                <div class="data-subtitle">${item.subtitle}</div>
            </div>
        `).join('');
    }
    
    // 获取标签名称
    getTabName(tab) {
        const names = {
            'overview': '数据总览',
            'audience': '受众数据',
            'content': '内容数据',
            'brand': '品牌数据'
        };
        return names[tab] || '数据';
    }
    
    // 渲染分页按钮
    renderPaginationNumbers() {
        let paginationHTML = '';
        
        for (let i = 1; i <= this.totalPages; i++) {
            paginationHTML += `
                <button class="page-number ${i === this.currentPage ? 'active' : ''}" data-page="${i}">
                    ${i}
                </button>
            `;
        }
        
        this.elements.paginationNumbers.innerHTML = paginationHTML;
        
        // 绑定页码点击事件
        this.elements.paginationNumbers.querySelectorAll('.page-number').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const page = parseInt(e.target.dataset.page);
                this.goToPage(page);
            });
        });
    }
    
    // 更新按钮状态
    updateButtonStates() {
        this.elements.prevBtn.disabled = this.currentPage === 1;
        this.elements.nextBtn.disabled = this.currentPage === this.totalPages;
    }
    
    // 初始化图表
    initializeChart() {
        if (!this.elements.chartCanvas) return;

        const ctx = this.elements.chartCanvas.getContext('2d');

        this.chart = new Chart(ctx, {
            type: this.currentChartType,
            data: this.getChartData(),
            options: this.getChartOptions()
        });
    }

    // 获取图表数据
    getChartData() {
        const currentData = this.growthData[this.currentDataType];

        return {
            labels: this.growthData.dates,
            datasets: [{
                label: currentData.label,
                data: currentData.data,
                borderColor: currentData.borderColor,
                backgroundColor: this.currentChartType === 'line' ? currentData.backgroundColor : currentData.barColor,
                borderWidth: 2,
                fill: this.currentChartType === 'line',
                tension: 0.4
            }]
        };
    }

    // 获取图表配置
    getChartOptions() {
        return {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        },
                        color: '#999',
                        font: {
                            size: 12
                        }
                    },
                    grid: {
                        color: '#f0f0f0'
                    }
                },
                x: {
                    ticks: {
                        color: '#999',
                        font: {
                            size: 12
                        }
                    },
                    grid: {
                        display: false
                    }
                }
            },
            elements: {
                point: {
                    radius: this.currentChartType === 'line' ? 3 : 0,
                    hoverRadius: 6
                }
            }
        };
    }

    // 更新图表
    updateChart() {
        if (!this.chart) return;

        this.chart.destroy();
        this.chart = new Chart(this.elements.chartCanvas.getContext('2d'), {
            type: this.currentChartType,
            data: this.getChartData(),
            options: this.getChartOptions()
        });
    }

    // 主渲染方法
    render() {
        this.renderDataCards();
        this.renderPaginationNumbers();
        this.updateButtonStates();

        // 添加页面切换动画
        this.elements.dataGrid.style.opacity = '0';
        setTimeout(() => {
            this.elements.dataGrid.style.opacity = '1';
        }, 100);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new NoxInfluencerDashboard();
});
