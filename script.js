// NOX聚星数据总览系统
class NoxInfluencerDashboard {
    constructor() {
        this.currentPage = 1;
        this.totalPages = 5;
        this.currentTab = 'overview';
        this.currentDataType = 'fans';
        this.currentChartType = 'line';
        this.chart = null;
        this.radarChart = null;

        this.initializeElements();
        this.generateMockData();
        this.bindEvents();
        this.render();
        this.initializeChart();
        this.initializeRadarChart();
        this.initializeAudienceCharts();
    }

    // 初始化受众数据图表
    initializeAudienceCharts() {
        if (this.currentTab !== 'audience') return;

        this.initializeAgeChart();
        this.initializeGenderChart();
        this.initializeRegionMap();
    }

    // 初始化年龄分布图表
    initializeAgeChart() {
        if (!this.elements.ageChart) return;

        const ctx = this.elements.ageChart.getContext('2d');

        // 销毁现有图表
        if (this.ageChart) {
            this.ageChart.destroy();
        }

        this.ageChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['13-17', '18-24', '25-34', '35-44', '45-54', '55-64', '65+'],
                datasets: [{
                    label: '受众比例',
                    data: [8.2, 24.3, 22.1, 18.7, 14.2, 8.9, 3.6],
                    backgroundColor: [
                        '#FF6B35',
                        '#FF8A65',
                        '#FFB74D',
                        '#81C784',
                        '#64B5F6',
                        '#9575CD',
                        '#F06292'
                    ],
                    borderColor: [
                        '#FF6B35',
                        '#FF8A65',
                        '#FFB74D',
                        '#81C784',
                        '#64B5F6',
                        '#9575CD',
                        '#F06292'
                    ],
                    borderWidth: 1,
                    borderRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.parsed.y + '%';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 30,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        grid: {
                            color: '#f0f0f0'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // 初始化性别分布图表
    initializeGenderChart() {
        if (!this.elements.genderChart) return;

        const ctx = this.elements.genderChart.getContext('2d');

        // 销毁现有图表
        if (this.genderChart) {
            this.genderChart.destroy();
        }

        this.genderChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['男性', '女性', '未知'],
                datasets: [{
                    data: [51.6, 46.8, 1.6],
                    backgroundColor: [
                        '#4285F4',
                        '#EA4335',
                        '#FBBC04'
                    ],
                    borderColor: '#fff',
                    borderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + '%';
                            }
                        }
                    }
                },
                cutout: '60%'
            }
        });
    }

    // 初始化地区地图（简化版）
    initializeRegionMap() {
        if (!this.elements.regionMap) return;

        const ctx = this.elements.regionMap.getContext('2d');
        const width = ctx.canvas.width;
        const height = ctx.canvas.height;

        // 清空画布
        ctx.clearRect(0, 0, width, height);

        // 绘制简化的世界地图
        ctx.fillStyle = '#e8f4fd';
        ctx.fillRect(0, 0, width, height);

        // 绘制主要国家/地区的简化形状
        const regions = [
            { name: '美国', x: 50, y: 80, size: 40, percentage: 68.7, color: '#4285F4' },
            { name: '加拿大', x: 60, y: 50, size: 15, percentage: 9.8, color: '#34A853' },
            { name: '英国', x: 140, y: 70, size: 8, percentage: 4.7, color: '#FBBC04' },
            { name: '澳大利亚', x: 220, y: 140, size: 12, percentage: 4.3, color: '#EA4335' },
            { name: '印度', x: 180, y: 100, size: 10, percentage: 3.5, color: '#9C27B0' }
        ];

        regions.forEach(region => {
            // 绘制圆点表示地区
            ctx.beginPath();
            ctx.arc(region.x, region.y, Math.sqrt(region.percentage) * 2, 0, 2 * Math.PI);
            ctx.fillStyle = region.color;
            ctx.fill();

            // 添加透明度效果
            ctx.globalAlpha = 0.7;
            ctx.beginPath();
            ctx.arc(region.x, region.y, Math.sqrt(region.percentage) * 3, 0, 2 * Math.PI);
            ctx.fillStyle = region.color;
            ctx.fill();
            ctx.globalAlpha = 1;
        });
    }
    
    // 初始化DOM元素
    initializeElements() {
        this.elements = {
            dataGrid: document.getElementById('dataGrid'),
            tabBtns: document.querySelectorAll('.tab-button'),
            growthTabs: document.querySelectorAll('.growth-tab'),
            chartControlBtns: document.querySelectorAll('.chart-control-btn'),
            chartCanvas: document.getElementById('growthChart'),
            radarCanvas: document.getElementById('radarChart'),
            fansCount: document.getElementById('fansCount'),
            viewsCount: document.getElementById('viewsCount'),
            commentsCount: document.getElementById('commentsCount'),
            // 受众数据相关元素
            basicDataSection: document.getElementById('basicDataSection'),
            audienceDataSection: document.getElementById('audienceDataSection'),
            ageChart: document.getElementById('ageDistributionChart'),
            genderChart: document.getElementById('genderDistributionChart'),
            regionMap: document.getElementById('audienceRegionMap')
        };
    }
    
    // 生成模拟数据
    generateMockData() {
        this.basicData = [
            {
                title: '平均观看量',
                value: '997.04万',
                change: '*****%',
                changeType: 'positive',
                subtitle: '6个月 1.06.04万 Shorts 810.74万',
                description: '各项指标表现良好，观看量稳定增长'
            },
            {
                title: '平均互动量',
                value: '44.91万',
                change: '+13.42%',
                changeType: 'positive',
                subtitle: '长视频 35.34万 Shorts 32.31万',
                description: '互动率持续提升，用户参与度高'
            },
            {
                title: '内容数量',
                value: '30',
                change: '0%',
                changeType: 'neutral',
                subtitle: '长视频 16 Shorts 14',
                description: '内容发布频率稳定'
            },
            {
                title: '观看量/粉丝数',
                value: '14.51%',
                change: '0%',
                changeType: 'neutral',
                subtitle: '长视频 18.48% Shorts 11.8%',
                description: '粉丝转化率表现良好'
            },
            {
                title: '预计曝光量',
                value: '982.61万',
                change: '',
                changeType: 'neutral',
                subtitle: '长视频 1099.03万 Shorts 774.76万',
                description: '预计曝光量持续增长'
            }
        ];

        // 生成增长数据
        this.generateGrowthData();

        // 生成雷达图数据
        this.generateRadarData();
    }

    // 生成增长数据
    generateGrowthData() {
        const dates = [];
        const today = new Date();

        // 生成过去20天的日期
        for (let i = 19; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            dates.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }));
        }

        this.growthData = {
            dates: dates,
            fans: {
                label: '粉丝数增长',
                data: this.generateRandomGrowthData(20, -2, 4),
                borderColor: '#1890ff',
                backgroundColor: 'rgba(24, 144, 255, 0.1)',
                barColor: '#1890ff'
            },
            views: {
                label: '播放量增长',
                data: this.generateRandomGrowthData(20, -3, 6),
                borderColor: '#52c41a',
                backgroundColor: 'rgba(82, 196, 26, 0.1)',
                barColor: '#52c41a'
            },
            posts: {
                label: '发布量增长',
                data: this.generateRandomGrowthData(20, -1, 3),
                borderColor: '#fa8c16',
                backgroundColor: 'rgba(250, 140, 22, 0.1)',
                barColor: '#fa8c16'
            }
        };
    }

    // 生成随机增长数据
    generateRandomGrowthData(count, min, max) {
        const data = [];
        for (let i = 0; i < count; i++) {
            const value = Math.random() * (max - min) + min;
            data.push(Math.round(value * 100) / 100);
        }
        return data;
    }

    // 生成雷达图数据
    generateRadarData() {
        this.radarData = {
            labels: ['内容质量', '粉丝增长', '互动质量', '发布频率', '品牌安全', '合作态度'],
            datasets: [{
                label: 'Nox评分指标',
                data: [85, 92, 78, 65, 88, 90], // 模拟评分数据 (0-100)
                backgroundColor: 'rgba(255, 107, 53, 0.2)',
                borderColor: '#FF6B35',
                borderWidth: 2,
                pointBackgroundColor: '#FF6B35',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: '#FF6B35'
            }]
        };
    }
    
    // 绑定事件
    bindEvents() {
        // 数据标签切换事件
        this.elements.tabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.target.dataset.tab;
                if (tab) {
                    this.switchTab(tab);
                }
            });
        });

        // 增长数据标签切换事件
        this.elements.growthTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const dataType = e.target.dataset.type;
                if (dataType) {
                    this.switchGrowthDataType(dataType);
                }
            });
        });

        // 图表控制按钮事件
        this.elements.chartControlBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const chartType = e.target.closest('.chart-control-btn').dataset.chart;
                if (chartType) {
                    this.switchChartType(chartType);
                }
            });
        });
    }
    
    // 切换数据标签
    switchTab(tab) {
        this.currentTab = tab;

        // 更新标签状态
        this.elements.tabBtns.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.tab === tab) {
                btn.classList.add('active');
            }
        });

        // 显示/隐藏对应的内容区域
        this.showTabContent(tab);
    }

    // 显示页签内容
    showTabContent(tab) {
        // 隐藏所有内容区域
        const allSections = [
            this.elements.basicDataSection,
            this.elements.audienceDataSection
        ];

        allSections.forEach(section => {
            if (section) {
                section.style.display = 'none';
            }
        });

        // 显示对应的内容区域
        switch(tab) {
            case 'overview':
                if (this.elements.basicDataSection) {
                    this.elements.basicDataSection.style.display = 'block';
                }
                this.render();
                break;
            case 'audience':
                if (this.elements.audienceDataSection) {
                    this.elements.audienceDataSection.style.display = 'block';
                    this.initializeAudienceCharts();
                }
                break;
            case 'content':
            case 'brand':
                // 显示敬请期待页面
                this.showComingSoon(tab);
                break;
        }
    }

    // 显示敬请期待页面
    showComingSoon(tab) {
        if (this.elements.basicDataSection) {
            this.elements.basicDataSection.style.display = 'block';
            this.elements.basicDataSection.innerHTML = `
                <div style="text-align: center; padding: 60px 20px; color: #999;">
                    <i class="fas fa-chart-line" style="font-size: 64px; margin-bottom: 20px; opacity: 0.3;"></i>
                    <h3 style="font-size: 24px; margin-bottom: 12px; color: #666;">敬请期待</h3>
                    <p style="font-size: 16px;">${this.getTabName(tab)}功能正在开发中</p>
                </div>
            `;
        }
    }
    

    
    // 切换增长数据类型
    switchGrowthDataType(dataType) {
        this.currentDataType = dataType;

        // 更新标签状态
        this.elements.growthTabs.forEach(tab => {
            tab.classList.remove('active');
            if (tab.dataset.type === dataType) {
                tab.classList.add('active');
            }
        });

        // 更新图表
        this.updateChart();
    }

    // 切换图表类型
    switchChartType(chartType) {
        this.currentChartType = chartType;

        // 更新按钮状态
        this.elements.chartControlBtns.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.chart === chartType) {
                btn.classList.add('active');
            }
        });

        // 更新图表
        this.updateChart();
    }


    
    // 渲染基本数据卡片
    renderDataCards() {
        if (this.currentTab !== 'overview' || !this.elements.dataGrid) {
            return;
        }
        
        this.elements.dataGrid.innerHTML = this.basicData.map(item => `
            <div class="data-card">
                <h4>${item.title}</h4>
                <div class="data-value">${item.value}</div>
                ${item.change ? `
                    <div class="data-change ${item.changeType}">
                        ${item.changeType === 'positive' ? '<i class="fas fa-arrow-up"></i>' : 
                          item.changeType === 'negative' ? '<i class="fas fa-arrow-down"></i>' : ''}
                        ${item.change}
                    </div>
                ` : ''}
                <div class="data-subtitle">${item.subtitle}</div>
            </div>
        `).join('');
    }
    
    // 获取标签名称
    getTabName(tab) {
        const names = {
            'overview': '数据总览',
            'audience': '受众数据',
            'content': '内容数据',
            'brand': '品牌数据'
        };
        return names[tab] || '数据';
    }
    

    
    // 初始化图表
    initializeChart() {
        if (!this.elements.chartCanvas) return;

        const ctx = this.elements.chartCanvas.getContext('2d');

        this.chart = new Chart(ctx, {
            type: this.currentChartType,
            data: this.getChartData(),
            options: this.getChartOptions()
        });
    }

    // 获取图表数据
    getChartData() {
        const currentData = this.growthData[this.currentDataType];

        return {
            labels: this.growthData.dates,
            datasets: [{
                label: currentData.label,
                data: currentData.data,
                borderColor: currentData.borderColor,
                backgroundColor: this.currentChartType === 'line' ? currentData.backgroundColor : currentData.barColor,
                borderWidth: 2,
                fill: this.currentChartType === 'line',
                tension: 0.4
            }]
        };
    }

    // 获取图表配置
    getChartOptions() {
        return {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        },
                        color: '#999',
                        font: {
                            size: 12
                        }
                    },
                    grid: {
                        color: '#f0f0f0'
                    }
                },
                x: {
                    ticks: {
                        color: '#999',
                        font: {
                            size: 12
                        }
                    },
                    grid: {
                        display: false
                    }
                }
            },
            elements: {
                point: {
                    radius: this.currentChartType === 'line' ? 3 : 0,
                    hoverRadius: 6
                }
            }
        };
    }

    // 更新图表
    updateChart() {
        if (!this.chart) return;

        this.chart.destroy();
        this.chart = new Chart(this.elements.chartCanvas.getContext('2d'), {
            type: this.currentChartType,
            data: this.getChartData(),
            options: this.getChartOptions()
        });
    }

    // 初始化雷达图
    initializeRadarChart() {
        if (!this.elements.radarCanvas) return;

        const ctx = this.elements.radarCanvas.getContext('2d');

        this.radarChart = new Chart(ctx, {
            type: 'radar',
            data: this.radarData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            display: false
                        },
                        grid: {
                            color: '#f0f0f0'
                        },
                        angleLines: {
                            color: '#f0f0f0'
                        },
                        pointLabels: {
                            font: {
                                size: 11
                            },
                            color: '#666'
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 4,
                        hoverRadius: 6
                    }
                }
            }
        });
    }

    // 主渲染方法
    render() {
        this.renderDataCards();

        // 添加页面切换动画
        if (this.elements.dataGrid) {
            this.elements.dataGrid.style.opacity = '0';
            setTimeout(() => {
                this.elements.dataGrid.style.opacity = '1';
            }, 100);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new NoxInfluencerDashboard();
});
