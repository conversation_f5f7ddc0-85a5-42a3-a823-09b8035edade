// 数据总览分页系统
class DataOverviewPagination {
    constructor() {
        this.currentPage = 1;
        this.itemsPerPage = 6;
        this.totalItems = 0;
        this.totalPages = 0;
        this.data = [];
        this.isLoading = false;
        
        this.initializeElements();
        this.generateMockData();
        this.bindEvents();
        this.render();
    }
    
    // 初始化DOM元素
    initializeElements() {
        this.elements = {
            dataGrid: document.getElementById('dataGrid'),
            loadingState: document.getElementById('loadingState'),
            emptyState: document.getElementById('emptyState'),
            prevBtn: document.getElementById('prevBtn'),
            nextBtn: document.getElementById('nextBtn'),
            paginationNumbers: document.getElementById('paginationNumbers'),
            paginationInfo: document.getElementById('paginationInfo'),
            itemsPerPageSelect: document.getElementById('itemsPerPage'),
            totalItems: document.getElementById('totalItems'),
            currentPage: document.getElementById('currentPage'),
            totalPages: document.getElementById('totalPages')
        };
    }
    
    // 生成模拟数据
    generateMockData() {
        const categories = ['营销数据', '用户行为', '销售统计', '流量分析', '转化率', '收入报告'];
        const statuses = ['正常', '警告', '异常'];
        const descriptions = [
            '本月数据表现良好，各项指标稳定增长',
            '用户活跃度持续上升，转化效果显著',
            '流量来源多样化，质量有所提升',
            '销售业绩超出预期，增长势头强劲',
            '数据异常波动，需要进一步分析',
            '关键指标达到历史新高'
        ];
        
        this.data = Array.from({ length: 87 }, (_, index) => ({
            id: index + 1,
            title: `${categories[index % categories.length]} #${String(index + 1).padStart(3, '0')}`,
            description: descriptions[index % descriptions.length],
            status: statuses[index % statuses.length],
            value: Math.floor(Math.random() * 10000) + 1000,
            date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('zh-CN'),
            category: categories[index % categories.length]
        }));
        
        this.totalItems = this.data.length;
        this.updateTotalPages();
    }
    
    // 绑定事件
    bindEvents() {
        // 上一页按钮
        this.elements.prevBtn.addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.goToPage(this.currentPage - 1);
            }
        });
        
        // 下一页按钮
        this.elements.nextBtn.addEventListener('click', () => {
            if (this.currentPage < this.totalPages) {
                this.goToPage(this.currentPage + 1);
            }
        });
        
        // 每页显示数量变更
        this.elements.itemsPerPageSelect.addEventListener('change', (e) => {
            this.itemsPerPage = parseInt(e.target.value);
            this.currentPage = 1;
            this.updateTotalPages();
            this.render();
        });
    }
    
    // 更新总页数
    updateTotalPages() {
        this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
    }
    
    // 跳转到指定页面
    goToPage(page) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) {
            return;
        }
        
        this.currentPage = page;
        this.render();
    }
    
    // 显示加载状态
    showLoading() {
        this.isLoading = true;
        this.elements.loadingState.style.display = 'block';
        this.elements.dataGrid.style.display = 'none';
        this.elements.emptyState.style.display = 'none';
    }
    
    // 隐藏加载状态
    hideLoading() {
        this.isLoading = false;
        this.elements.loadingState.style.display = 'none';
    }
    
    // 获取当前页数据
    getCurrentPageData() {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        return this.data.slice(startIndex, endIndex);
    }
    
    // 渲染数据卡片
    renderDataCards() {
        const currentData = this.getCurrentPageData();
        
        if (currentData.length === 0) {
            this.elements.dataGrid.style.display = 'none';
            this.elements.emptyState.style.display = 'block';
            return;
        }
        
        this.elements.emptyState.style.display = 'none';
        this.elements.dataGrid.style.display = 'grid';
        
        // 添加淡出效果
        this.elements.dataGrid.classList.add('fade-out');
        
        setTimeout(() => {
            this.elements.dataGrid.innerHTML = currentData.map((item, index) => `
                <div class="data-card" style="animation-delay: ${index * 0.1}s">
                    <h4>${item.title}</h4>
                    <p>${item.description}</p>
                    <div class="data-meta">
                        <span>${item.date}</span>
                        <span class="data-badge ${item.status === '正常' ? '' : item.status === '警告' ? 'warning' : 'error'}">
                            ${item.status}
                        </span>
                    </div>
                </div>
            `).join('');
            
            // 移除淡出效果
            this.elements.dataGrid.classList.remove('fade-out');
        }, 150);
    }
    
    // 渲染分页按钮
    renderPaginationNumbers() {
        const maxVisiblePages = 7;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(this.totalPages, startPage + maxVisiblePages - 1);
        
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }
        
        let paginationHTML = '';
        
        // 第一页
        if (startPage > 1) {
            paginationHTML += `<button class="page-number" data-page="1">1</button>`;
            if (startPage > 2) {
                paginationHTML += `<span class="page-ellipsis">...</span>`;
            }
        }
        
        // 页码按钮
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="page-number ${i === this.currentPage ? 'active' : ''}" data-page="${i}">
                    ${i}
                </button>
            `;
        }
        
        // 最后一页
        if (endPage < this.totalPages) {
            if (endPage < this.totalPages - 1) {
                paginationHTML += `<span class="page-ellipsis">...</span>`;
            }
            paginationHTML += `<button class="page-number" data-page="${this.totalPages}">${this.totalPages}</button>`;
        }
        
        this.elements.paginationNumbers.innerHTML = paginationHTML;
        
        // 绑定页码点击事件
        this.elements.paginationNumbers.querySelectorAll('.page-number').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const page = parseInt(e.target.dataset.page);
                this.goToPage(page);
            });
        });
    }
    
    // 更新分页信息
    updatePaginationInfo() {
        const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
        
        this.elements.paginationInfo.textContent = 
            `显示第 ${startItem}-${endItem} 条，共 ${this.totalItems} 条数据`;
    }
    
    // 更新按钮状态
    updateButtonStates() {
        this.elements.prevBtn.disabled = this.currentPage === 1;
        this.elements.nextBtn.disabled = this.currentPage === this.totalPages;
    }
    
    // 更新统计信息
    updateStats() {
        this.elements.totalItems.textContent = this.totalItems;
        this.elements.currentPage.textContent = this.currentPage;
        this.elements.totalPages.textContent = this.totalPages;
    }
    
    // 主渲染方法
    render() {
        this.showLoading();
        
        // 模拟加载延迟
        setTimeout(() => {
            this.hideLoading();
            this.renderDataCards();
            this.renderPaginationNumbers();
            this.updatePaginationInfo();
            this.updateButtonStates();
            this.updateStats();
            
            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }, 500);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new DataOverviewPagination();
});
