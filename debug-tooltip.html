<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具提示调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .horizontal-bar-container {
            width: 100%;
            padding: 0 92px 0 80px;
        }
        
        .horizontal-bar {
            display: flex;
            width: 100%;
            height: 24px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .bar-segment {
            height: 100%;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
        }
        
        .bar-segment:hover {
            opacity: 0.8;
            transform: scaleY(1.1);
        }
        
        /* 工具提示样式 */
        .tooltip {
            position: absolute;
            background-color: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            z-index: 1000;
            pointer-events: none;
            opacity: 0;
            transform: translateY(-100%) translateX(-50%);
            transition: opacity 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
        }
        
        .tooltip.show {
            opacity: 1;
        }
        
        .tooltip-icon {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
            vertical-align: middle;
        }
        
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>工具提示调试页面</h1>
        
        <div id="debug-info" class="debug-info">
            <strong>调试信息：</strong><br>
            <div id="status-container"></div>
        </div>
        
        <div class="test-card">
            <h3>受众区域测试</h3>
            <div class="horizontal-bar-container">
                <div class="horizontal-bar">
                    <div class="bar-segment" data-region="美国" data-percentage="68.7" style="background-color: #4285F4; width: 68.7%;"></div>
                    <div class="bar-segment" data-region="加拿大" data-percentage="9.8" style="background-color: #34A853; width: 9.8%;"></div>
                    <div class="bar-segment" data-region="英国" data-percentage="4.7" style="background-color: #FBBC04; width: 4.7%;"></div>
                    <div class="bar-segment" data-region="澳大利亚" data-percentage="4.3" style="background-color: #EA4335; width: 4.3%;"></div>
                    <div class="bar-segment" data-region="印度" data-percentage="3.5" style="background-color: #9C27B0; width: 3.5%;"></div>
                    <div class="bar-segment" data-region="德国" data-percentage="3.1" style="background-color: #FF9800; width: 3.1%;"></div>
                    <div class="bar-segment" data-region="其他" data-percentage="5.9" style="background-color: #607D8B; width: 5.9%;"></div>
                </div>
            </div>
        </div>
        
        <div class="test-card">
            <h3>受众语言测试</h3>
            <div class="horizontal-bar-container">
                <div class="horizontal-bar">
                    <div class="bar-segment" data-language="英语" data-percentage="84.5" style="background-color: #4285F4; width: 84.5%;"></div>
                    <div class="bar-segment" data-language="西班牙语" data-percentage="7.3" style="background-color: #FF9800; width: 7.3%;"></div>
                    <div class="bar-segment" data-language="葡萄牙语" data-percentage="6.4" style="background-color: #34A853; width: 6.4%;"></div>
                    <div class="bar-segment" data-language="其他语言" data-percentage="1.8" style="background-color: #607D8B; width: 1.8%;"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function addStatus(message, type = 'success') {
            const container = document.getElementById('status-container');
            const status = document.createElement('div');
            status.className = `status ${type}`;
            status.textContent = message;
            container.appendChild(status);
        }
        
        // 初始化工具提示
        function initializeBarSegmentTooltips() {
            const barSegments = document.querySelectorAll('.bar-segment');
            addStatus(`找到 ${barSegments.length} 个条形图分段`);
            
            if (barSegments.length === 0) {
                addStatus('错误：没有找到任何条形图分段', 'error');
                return;
            }
            
            barSegments.forEach((segment, index) => {
                addStatus(`初始化分段 ${index + 1}: ${segment.dataset.region || segment.dataset.language}`);
                
                // 创建工具提示元素
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip';
                document.body.appendChild(tooltip);
                
                // 鼠标进入事件
                segment.addEventListener('mouseenter', (e) => {
                    const region = segment.dataset.region || segment.dataset.language;
                    const percentage = segment.dataset.percentage;
                    const color = segment.style.backgroundColor;
                    
                    console.log('Tooltip triggered:', { region, percentage, color });
                    
                    // 设置工具提示内容
                    tooltip.innerHTML = `
                        <span class="tooltip-icon" style="background-color: ${color};"></span>
                        <strong>${percentage}%</strong> ${region}
                    `;
                    
                    // 显示工具提示
                    tooltip.classList.add('show');
                    updateTooltipPosition(e, tooltip);
                });
                
                // 鼠标移动事件
                segment.addEventListener('mousemove', (e) => {
                    updateTooltipPosition(e, tooltip);
                });
                
                // 鼠标离开事件
                segment.addEventListener('mouseleave', () => {
                    tooltip.classList.remove('show');
                });
            });
            
            addStatus('工具提示初始化完成！');
        }
        
        // 更新工具提示位置
        function updateTooltipPosition(event, tooltip) {
            const rect = event.target.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();
            
            // 计算位置
            let left = rect.left + (rect.width / 2);
            let top = rect.top - 10;
            
            // 防止工具提示超出屏幕边界
            if (left + tooltipRect.width / 2 > window.innerWidth) {
                left = window.innerWidth - tooltipRect.width / 2 - 10;
            }
            if (left - tooltipRect.width / 2 < 0) {
                left = tooltipRect.width / 2 + 10;
            }
            
            tooltip.style.left = left + 'px';
            tooltip.style.top = top + 'px';
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            addStatus('DOM 加载完成');
            setTimeout(() => {
                addStatus('开始初始化工具提示...');
                initializeBarSegmentTooltips();
            }, 100);
        });
    </script>
</body>
</html>
