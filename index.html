<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube 网红信息 - NOX聚星</title>
    <link href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.net/tailwindcss/2.2.9/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="top-header">
        <div class="header-left">
            <div class="logo">
                <span class="nox-text">NOX</span><span class="star-text">聚星</span>
            </div>
            <h1 class="page-title">YouTube 网红信息</h1>
        </div>
        <div class="header-center">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="搜索网红">
            </div>
        </div>
        <div class="header-right">
            <button class="header-btn price-btn">
                <i class="fas fa-crown"></i>
                价格
            </button>
            <button class="header-btn">
                <i class="fas fa-bell"></i>
            </button>
            <button class="header-btn">
                <i class="fas fa-comment"></i>
            </button>
            <button class="header-btn">
                <i class="fas fa-th"></i>
            </button>
            <div class="language-selector">
                <span>简体中文</span>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
        </div>
    </header>

    <div class="main-container">
        <!-- 左侧导航栏 -->
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <div class="nav-item active">
                    <i class="fas fa-search"></i>
                    <span>网红搜索</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-shopping-bag"></i>
                    <span>TT Shop管家</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span>TT营销洞察</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-star"></i>
                    <span>我的网红</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>网红列表</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>合作管理</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-bullhorn"></i>
                    <span>行业洞察</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>设置</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-question-circle"></i>
                    <span>AI营销知识库</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-robot"></i>
                    <span>效果追踪</span>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 网红信息卡片 - 完全按照原设计图还原 -->
            <section class="bento-card influencer-main-card">
                <!-- 更新时间标签 -->
                <div class="update-time-label">
                    更新时间：2025-07-18 04:05:04 <span class="update-status">已更新</span>
                </div>

                <!-- 主要信息区域 -->
                <div class="influencer-main-content">
                    <!-- 左侧：头像和基本信息 -->
                    <div class="influencer-left-section">
                        <div class="avatar-container">
                            <img src="https://via.placeholder.com/80x80/FF6B35/FFFFFF?text=MB" alt="MrBeast" class="influencer-avatar">
                        </div>
                        <div class="influencer-basic-info">
                            <div class="influencer-name-row">
                                <h2 class="influencer-name">MrBeast</h2>
                                <span class="influencer-username">@mrbeast</span>
                                <span class="influencer-category">娱乐</span>
                            </div>
                            <div class="influencer-location-row">
                                <img src="https://via.placeholder.com/16x12/FF0000/FFFFFF?text=US" alt="美国" class="country-flag">
                                <span class="country-name">美国</span>
                                <span class="verified-badge">已认证</span>
                                <span class="subscriber-badge">1.1万</span>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：操作按钮 -->
                    <div class="influencer-right-section">
                        <div class="action-buttons-row">
                            <button class="btn-ai-analysis">
                                <i class="fas fa-robot"></i>
                                AI相似分析
                            </button>
                            <button class="btn-icon-action btn-favorite">
                                <i class="far fa-heart"></i>
                            </button>
                            <button class="btn-icon-action btn-copy">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="btn-icon-action btn-share">
                                <i class="fas fa-share"></i>
                            </button>
                            <button class="btn-youtube-link">
                                <i class="fab fa-youtube"></i>
                                YouTube
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 关键指标条 -->
                <div class="key-metrics-bar">
                    <div class="metric-item">
                        <div class="metric-label">粉丝数</div>
                        <div class="metric-value">4.14亿</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">最近发布时间</div>
                        <div class="metric-value">7天前</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">最近更新时间</div>
                        <div class="metric-value">13天前</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">NoxScore</div>
                        <div class="metric-value">4.56</div>
                    </div>
                    <div class="metric-item">
                        <div class="metric-label">合作指数</div>
                        <div class="metric-value">6/10</div>
                    </div>
                </div>
            </section>

            <!-- 数据分类标签卡片 -->
            <section class="bento-card data-tabs-card">
                <div class="card-header">
                    <h3>数据分类</h3>
                </div>
                <div class="data-tabs">
                    <button class="tab-btn active" data-tab="overview">
                        <i class="fas fa-chart-pie"></i>
                        数据总览
                    </button>
                    <button class="tab-btn" data-tab="audience">
                        <i class="fas fa-users"></i>
                        受众数据
                    </button>
                    <button class="tab-btn" data-tab="content">
                        <i class="fas fa-video"></i>
                        内容数据
                    </button>
                    <button class="tab-btn" data-tab="brand">
                        <i class="fas fa-handshake"></i>
                        品牌数据
                    </button>
                </div>
            </section>

            <!-- 基本数据区域卡片 -->
            <section class="bento-card basic-data-card" id="basicDataSection">
                <div class="card-header">
                    <h3>
                        <i class="fas fa-chart-bar"></i>
                        基本数据
                    </h3>
                    <span class="time-filter">近30个月</span>
                </div>

                <div class="data-grid" id="dataGrid">
                    <!-- 数据卡片将通过JavaScript动态生成 -->
                </div>
            </section>

            <!-- 增长数据区域 -->
            <section class="growth-data">
                <h3 class="section-title">
                    <i class="fas fa-trending-up"></i>
                    增长数据
                </h3>

                <div class="growth-cards">
                    <div class="growth-card">
                        <div class="growth-icon blue">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="growth-info">
                            <div class="growth-label">粉丝数</div>
                            <div class="growth-value" id="fansCount">68,700,000</div>
                        </div>
                    </div>
                    <div class="growth-card">
                        <div class="growth-icon green">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="growth-info">
                            <div class="growth-label">观看量</div>
                            <div class="growth-value" id="viewsCount">21,550,277,449</div>
                        </div>
                    </div>
                    <div class="growth-card">
                        <div class="growth-icon orange">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="growth-info">
                            <div class="growth-label">评论</div>
                            <div class="growth-value" id="commentsCount">1,553</div>
                        </div>
                    </div>
                </div>

                <!-- 增长数据标签和图表控制 -->
                <div class="growth-tabs-container">
                    <div class="growth-tabs">
                        <button class="growth-tab active" data-type="fans">粉丝数增长</button>
                        <button class="growth-tab" data-type="views">播放量增长</button>
                        <button class="growth-tab" data-type="posts">发布量增长</button>
                    </div>
                    <div class="chart-controls">
                        <button class="chart-control-btn active" data-chart="line" title="折线图">
                            <i class="fas fa-chart-line"></i>
                        </button>
                        <button class="chart-control-btn" data-chart="bar" title="柱状图">
                            <i class="fas fa-chart-bar"></i>
                        </button>
                    </div>
                </div>

                <!-- 图表容器 -->
                <div class="chart-container">
                    <canvas id="growthChart"></canvas>
                </div>

                <!-- 图表说明 -->
                <div class="chart-description">
                    <p class="chart-note">增长数据统计：基于过去20天的数据统计分析</p>
                    <p class="chart-note">增长数据来源：基于平台公开数据</p>
                </div>
            </section>

            <!-- 频道质量区域 -->
            <section class="channel-quality">
                <h3 class="section-title">
                    <i class="fas fa-star"></i>
                    频道质量
                </h3>

                <!-- Bento Grid 卡片布局 -->
                <div class="quality-bento-grid">
                    <!-- NoxScore评分卡片 -->
                    <div class="quality-card nox-score-card">
                        <div class="card-header">
                            <h4>NoxScore</h4>
                            <span class="score-badge excellent">优秀</span>
                        </div>
                        <div class="score-display">
                            <span class="score-number">4.56</span>
                            <span class="score-suffix">分</span>
                        </div>
                        <div class="star-rating">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p class="card-description">
                            NoxScore基于主要指标的综合表现，包括内容质量、频道活跃度、粉丝互动等多个维度的评估。
                        </p>
                    </div>

                    <!-- Nox评分指标雷达图卡片 -->
                    <div class="quality-card radar-chart-card">
                        <div class="card-header">
                            <h4>Nox评分指标</h4>
                        </div>
                        <div class="radar-container">
                            <canvas id="radarChart"></canvas>
                        </div>
                    </div>

                    <!-- 详细指标评分卡片 -->
                    <div class="quality-card detailed-scores-card">
                        <div class="detailed-scores">
                            <div class="score-item">
                                <span class="score-label">粉丝增长</span>
                                <div class="score-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="score-level excellent">优秀</span>
                            </div>
                            <div class="score-item">
                                <span class="score-label">创作频率</span>
                                <div class="score-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                                <span class="score-level average">中等</span>
                            </div>
                            <div class="score-item">
                                <span class="score-label">频道质量</span>
                                <div class="score-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="score-level excellent">优秀</span>
                            </div>
                            <div class="score-item">
                                <span class="score-label">互动率</span>
                                <div class="score-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                                <span class="score-level good">良好</span>
                            </div>
                            <div class="score-item">
                                <span class="score-label">粉丝可信度</span>
                                <div class="score-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="score-level excellent">优秀</span>
                            </div>
                        </div>
                    </div>

                    <!-- 合作指向和标签卡片 -->
                    <div class="quality-card cooperation-card">
                        <div class="cooperation-content">
                            <div class="cooperation-score-section">
                                <div class="card-header">
                                    <h4>合作指向</h4>
                                </div>
                                <div class="cooperation-rating">
                                    <span class="cooperation-number">6</span>
                                    <span class="cooperation-divider">/</span>
                                    <span class="cooperation-total">10</span>
                                </div>
                                <p class="cooperation-description">
                                    次数：清晰活跃，买一送一作品数
                                </p>
                                <p class="cooperation-note">
                                    该指标反映了合作意向和合作质量
                                </p>
                            </div>

                            <div class="cooperation-tags-section">
                                <div class="card-header">
                                    <h4>合作行为标签</h4>
                                    <span class="tags-indicator">反馈频繁</span>
                                </div>
                                <div class="tag-container">
                                    <span class="tag">主动发布宣传视频</span>
                                    <span class="tag">主动发布宣传动态</span>
                                    <span class="tag">积极配合</span>
                                    <span class="tag">主动提供数据截图</span>
                                    <span class="tag">NoxScore参与度</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 分页组件卡片 -->
            <section class="bento-card pagination-card">
                <div class="card-header">
                    <h3>
                        <i class="fas fa-list"></i>
                        数据导航
                    </h3>
                </div>

                <div class="pagination-content">
                    <div class="pagination-tabs">
                        <button class="pagination-tab active">粉丝数增长</button>
                        <button class="pagination-tab">播放量增长</button>
                        <button class="pagination-tab">发布量增长</button>
                    </div>

                    <div class="pagination-controls">
                        <button class="pagination-btn" id="prevBtn">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div class="pagination-numbers" id="paginationNumbers">
                            <!-- 页码将通过JavaScript生成 -->
                        </div>
                        <button class="pagination-btn" id="nextBtn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
