<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube 网红信息 - NOX聚星</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="top-header">
        <div class="header-left">
            <div class="logo">
                <span class="nox-text">NOX</span><span class="star-text">聚星</span>
            </div>
            <h1 class="page-title">YouTube 网红信息</h1>
        </div>
        <div class="header-center">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="搜索网红">
            </div>
        </div>
        <div class="header-right">
            <button class="header-btn price-btn">
                <i class="fas fa-crown"></i>
                价格
            </button>
            <button class="header-btn">
                <i class="fas fa-bell"></i>
            </button>
            <button class="header-btn">
                <i class="fas fa-comment"></i>
            </button>
            <button class="header-btn">
                <i class="fas fa-th"></i>
            </button>
            <div class="language-selector">
                <span>简体中文</span>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
        </div>
    </header>

    <div class="main-container">
        <!-- 左侧导航栏 -->
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <div class="nav-item active">
                    <i class="fas fa-search"></i>
                    <span>网红搜索</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-shopping-bag"></i>
                    <span>TT Shop管家</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-chart-line"></i>
                    <span>TT营销洞察</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-star"></i>
                    <span>我的网红</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>网红列表</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>合作管理</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-bullhorn"></i>
                    <span>行业洞察</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>设置</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-question-circle"></i>
                    <span>AI营销知识库</span>
                </div>
                <div class="nav-item">
                    <i class="fas fa-robot"></i>
                    <span>效果追踪</span>
                </div>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 网红信息卡片 -->
            <section class="influencer-card">
                <div class="update-time">
                    更新时间：2025-07-18 04:05:04 <span class="update-status">已更新</span>
                </div>

                <div class="influencer-info">
                    <div class="avatar-section">
                        <img src="https://via.placeholder.com/80x80/FF6B35/FFFFFF?text=FV" alt="Fede Vigevani" class="avatar">
                        <div class="basic-info">
                            <h2 class="name">Fede Vigevani <span class="username">@fedevigevani</span></h2>
                            <div class="location">
                                <img src="https://via.placeholder.com/16x12/FF0000/FFFFFF" alt="乌拉圭" class="flag">
                                <span>乌拉圭</span>
                                <span class="verified">已认证</span>
                                <span class="subscriber-count">1.1万</span>
                            </div>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="btn-ai-similar">
                            <i class="fas fa-robot"></i>
                            AI相似分析
                        </button>
                        <button class="btn-favorite">
                            <i class="far fa-heart"></i>
                        </button>
                        <button class="btn-copy">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button class="btn-share">
                            <i class="fas fa-share"></i>
                        </button>
                        <button class="btn-youtube">
                            <i class="fab fa-youtube"></i>
                            YouTube
                        </button>
                    </div>
                </div>
            </section>

            <!-- 关键指标 -->
            <section class="key-metrics">
                <div class="metric-item">
                    <div class="metric-label">粉丝数</div>
                    <div class="metric-value">687万</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">发布频率</div>
                    <div class="metric-value">4天前</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">最近更新时间</div>
                    <div class="metric-value">5天前</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">NoxScore</div>
                    <div class="metric-value">4.77</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">合作评级</div>
                    <div class="metric-value">5/10</div>
                </div>
            </section>

            <!-- 数据分类标签 -->
            <section class="data-tabs">
                <button class="tab-btn active" data-tab="overview">
                    <i class="fas fa-chart-pie"></i>
                    数据总览
                </button>
                <button class="tab-btn" data-tab="audience">
                    <i class="fas fa-users"></i>
                    受众数据
                </button>
                <button class="tab-btn" data-tab="content">
                    <i class="fas fa-video"></i>
                    内容数据
                </button>
                <button class="tab-btn" data-tab="brand">
                    <i class="fas fa-handshake"></i>
                    品牌数据
                </button>
            </section>

            <!-- 基本数据区域 -->
            <section class="basic-data" id="basicDataSection">
                <h3 class="section-title">
                    <i class="fas fa-chart-bar"></i>
                    基本数据
                    <span class="time-filter">近30个月</span>
                </h3>

                <div class="data-grid" id="dataGrid">
                    <!-- 数据卡片将通过JavaScript动态生成 -->
                </div>
            </section>

            <!-- 增长数据区域 -->
            <section class="growth-data">
                <h3 class="section-title">
                    <i class="fas fa-trending-up"></i>
                    增长数据
                </h3>

                <div class="growth-cards">
                    <div class="growth-card">
                        <div class="growth-icon blue">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="growth-info">
                            <div class="growth-label">粉丝数</div>
                            <div class="growth-value" id="fansCount">68,700,000</div>
                        </div>
                    </div>
                    <div class="growth-card">
                        <div class="growth-icon green">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="growth-info">
                            <div class="growth-label">观看量</div>
                            <div class="growth-value" id="viewsCount">21,550,277,449</div>
                        </div>
                    </div>
                    <div class="growth-card">
                        <div class="growth-icon orange">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="growth-info">
                            <div class="growth-label">评论</div>
                            <div class="growth-value" id="commentsCount">1,553</div>
                        </div>
                    </div>
                </div>

                <!-- 增长数据标签和图表控制 -->
                <div class="growth-tabs-container">
                    <div class="growth-tabs">
                        <button class="growth-tab active" data-type="fans">粉丝数增长</button>
                        <button class="growth-tab" data-type="views">播放量增长</button>
                        <button class="growth-tab" data-type="posts">发布量增长</button>
                    </div>
                    <div class="chart-controls">
                        <button class="chart-control-btn active" data-chart="line" title="折线图">
                            <i class="fas fa-chart-line"></i>
                        </button>
                        <button class="chart-control-btn" data-chart="bar" title="柱状图">
                            <i class="fas fa-chart-bar"></i>
                        </button>
                    </div>
                </div>

                <!-- 图表容器 -->
                <div class="chart-container">
                    <canvas id="growthChart"></canvas>
                </div>

                <!-- 图表说明 -->
                <div class="chart-description">
                    <p class="chart-note">增长数据统计：基于过去20天的数据统计分析</p>
                    <p class="chart-note">增长数据来源：基于平台公开数据</p>
                </div>
            </section>

            <!-- 分页组件 -->
            <section class="pagination-section">
                <div class="pagination-tabs">
                    <button class="pagination-tab active">粉丝数增长</button>
                    <button class="pagination-tab">播放量增长</button>
                    <button class="pagination-tab">发布量增长</button>
                </div>

                <div class="pagination-controls">
                    <button class="pagination-btn" id="prevBtn">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="pagination-numbers" id="paginationNumbers">
                        <!-- 页码将通过JavaScript生成 -->
                    </div>
                    <button class="pagination-btn" id="nextBtn">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </section>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
