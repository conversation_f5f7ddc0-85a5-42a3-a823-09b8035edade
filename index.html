<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据总览 - 分页原型</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <h1 class="logo">
                    <i class="fas fa-chart-bar"></i>
                    数据总览
                </h1>
                <nav class="nav-menu">
                    <a href="#" class="nav-item active">总览</a>
                    <a href="#" class="nav-item">内容数据</a>
                    <a href="#" class="nav-item">受众数据</a>
                    <a href="#" class="nav-item">品牌数据</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <div class="container">
            <!-- 页面标题和统计信息 -->
            <section class="page-header">
                <h2 class="page-title">数据总览</h2>
                <div class="stats-summary">
                    <div class="stat-item">
                        <span class="stat-number" id="totalItems">0</span>
                        <span class="stat-label">总数据量</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="currentPage">1</span>
                        <span class="stat-label">当前页</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="totalPages">0</span>
                        <span class="stat-label">总页数</span>
                    </div>
                </div>
            </section>

            <!-- 数据展示区域 -->
            <section class="data-section">
                <div class="data-header">
                    <h3>数据列表</h3>
                    <div class="data-controls">
                        <select id="itemsPerPage" class="items-per-page">
                            <option value="6">每页 6 条</option>
                            <option value="12">每页 12 条</option>
                            <option value="24">每页 24 条</option>
                        </select>
                    </div>
                </div>
                
                <!-- 加载状态 -->
                <div class="loading-state" id="loadingState">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>加载中...</span>
                    </div>
                </div>

                <!-- 数据网格 -->
                <div class="data-grid" id="dataGrid">
                    <!-- 数据卡片将通过JavaScript动态生成 -->
                </div>

                <!-- 空状态 -->
                <div class="empty-state" id="emptyState" style="display: none;">
                    <i class="fas fa-inbox"></i>
                    <h3>暂无数据</h3>
                    <p>当前页面没有可显示的数据</p>
                </div>
            </section>

            <!-- 分页组件 -->
            <section class="pagination-section">
                <div class="pagination-wrapper">
                    <div class="pagination-info">
                        <span id="paginationInfo">显示第 1-6 条，共 0 条数据</span>
                    </div>
                    
                    <nav class="pagination" id="pagination">
                        <button class="pagination-btn prev-btn" id="prevBtn" disabled>
                            <i class="fas fa-chevron-left"></i>
                            上一页
                        </button>
                        
                        <div class="pagination-numbers" id="paginationNumbers">
                            <!-- 页码按钮将通过JavaScript动态生成 -->
                        </div>
                        
                        <button class="pagination-btn next-btn" id="nextBtn">
                            下一页
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </nav>
                </div>
            </section>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 数据总览系统. 保留所有权利.</p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
